package io.hydrax.aeron.connection;

import java.util.ArrayList;
import java.util.List;
import org.agrona.concurrent.Agent;

public class KeepAliveAgent implements Agent {
  final List<KeepAliveProcessor> keepAliveProcessors = new ArrayList<>();

  @Override
  public int doWork() throws Exception {
    for (KeepAliveProcessor keepAliveProcessor : keepAliveProcessors) {
      keepAliveProcessor.process();
    }
    return keepAliveProcessors.size();
  }

  @Override
  public String roleName() {
    return "cluster client keep alive";
  }

  public void addKeepAliveProcessor(KeepAliveProcessor keepAliveProcessor) {
    keepAliveProcessors.add(keepAliveProcessor);
  }
}
