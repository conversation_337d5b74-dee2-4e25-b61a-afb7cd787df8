package io.hydrax.aeron.connection;

import io.aeron.cluster.client.AeronCluster;
import io.hydrax.aeron.common.IdelStrategyEnum;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.AtomicReference;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

@Getter
@Slf4j
public class KeepAliveProcessor {
  final AtomicReference<AeronCluster> aeronCluster = new AtomicReference<>();
  AtomicLong keepAliveDeadlineMs = new AtomicLong(0);

  public void process() {
    if (aeronCluster.get() == null || aeronCluster.get().isClosed()) {
      return;
    }
    @SuppressWarnings("squid:S2245")
    int randomNumber = ThreadLocalRandom.current().nextInt(500 - 100 + 1) + 100;
    keepAliveDeadlineMs.set(System.currentTimeMillis() + randomNumber);
    final long currentTimeMs = System.currentTimeMillis();
    if (keepAliveDeadlineMs.get() <= currentTimeMs) {
      boolean keepAlive = aeronCluster.get().sendKeepAlive();
      log.trace(
          "Sending keep alive: {}, channel: {}",
          keepAlive,
          aeronCluster.get().ingressPublication().channel());
      if (!keepAlive) {
        aeronCluster.get().close();
        return;
      }
      IdelStrategyEnum.SLEEPING.getIdleStrategy().idle(aeronCluster.get().pollEgress());
      keepAliveDeadlineMs.addAndGet(500);
    }
  }
}
