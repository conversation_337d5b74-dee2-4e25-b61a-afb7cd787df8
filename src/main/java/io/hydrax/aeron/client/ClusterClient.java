package io.hydrax.aeron.client;

import io.aeron.Publication;
import io.aeron.cluster.client.AeronCluster;
import io.aeron.exceptions.TimeoutException;
import io.hydrax.aeron.AeronMessage;
import io.hydrax.aeron.ConnectionException;
import io.hydrax.aeron.InsufficientException;
import io.hydrax.aeron.config.AeronClientProperty;
import io.hydrax.aeron.connection.ExponentialBackoffReconnectStrategy;
import io.hydrax.aeron.connection.KeepAliveProcessor;
import io.hydrax.aeron.connection.ReconnectStrategy;
import io.netty.util.concurrent.FastThreadLocal;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Consumer;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.agrona.concurrent.AtomicBuffer;
import org.agrona.concurrent.IdleStrategy;
import org.agrona.concurrent.UnsafeBuffer;

@Slf4j
@EqualsAndHashCode(exclude = "idleStrategy")
@Data
public class ClusterClient implements AeronClient {

  final FastThreadLocal<AtomicBuffer> buffer = new FastThreadLocal<>();
  final Consumer<byte[]> consumer;
  private final IdleStrategy idleStrategy;
  private final AeronClientProperty.Client client;
  private final String aeronDirectory;
  private static final AtomicInteger ID = new AtomicInteger(0);
  private final ReconnectStrategy reconnectStrategy = new ExponentialBackoffReconnectStrategy(5);
  private final KeepAliveProcessor keepAliveProcessor;

  AeronCluster aeronCluster;

  public ClusterClient(
      Consumer<byte[]> consumer,
      IdleStrategy idleStrategy,
      AeronClientProperty.Client client,
      String aeronDirectory,
      KeepAliveProcessor keepAliveProcessor) {
    this.consumer = consumer;
    this.idleStrategy = idleStrategy;
    this.client = client;
    this.aeronDirectory = aeronDirectory;
    this.keepAliveProcessor = keepAliveProcessor;
  }

  public boolean start() {
    synchronized (this) {
      if (aeronCluster == null || aeronCluster.isClosed()) {
        log.debug(
            "Starting Cluster Client With Property: name: {}, egressChannel: {}, ingressChannel:"
                + " {}, ingressEndpoints: {}, ingressStreamId: {}",
            client.targetName(),
            client.egressChannel(),
            client.ingressChannel(),
            client.ingressEndpoints(),
            client.ingressStreamId());
        DefaultListener listener = new DefaultListener(consumer);
        try {
          aeronCluster =
              AeronCluster.connect(
                  new AeronCluster.Context()
                      .isIngressExclusive(false)
                      .egressListener(listener)
                      .egressChannel(
                          client.egressChannel().orElse(AeronCluster.Configuration.egressChannel()))
                      .aeronDirectoryName(aeronDirectory)
                      .ingressChannel(client.ingressChannel())
                      .ingressEndpoints(client.ingressEndpoints())
                      .ingressStreamId(client.ingressStreamId()));
          keepAliveProcessor.getAeronCluster().set(aeronCluster);
          return true;
        } catch (TimeoutException e) {
          log.error("cluster timout count: {}", reconnectStrategy.getRetryCount(), e);
          return false;
        }
      }
    }
    return false;
  }

  @Override
  public boolean send(AeronMessage message, long sequence)
      throws ConnectionException, InsufficientException {
    if (!connectionAvailable() || reconnectStrategy.isRunning()) {
      throw new ConnectionException("Connection not available", client.targetName());
    }
    if (!isConnected() && !reconnectStrategy.reconnect(this::start)) {
      throw new ConnectionException("Failed to reconnect", client.targetName(), message);
    }
    if (!buffer.isSet()) {
      buffer.set(new UnsafeBuffer(new byte[30000]));
    }
    byte[] data;
    data = message.toByteArray();
    if (data == null) {
      return false;
    }
    int messageLength = data.length;
    AtomicBuffer atomicBuffer = buffer.get();
    atomicBuffer.putLong(0, client.clientId());
    atomicBuffer.putLong(8, sequence);
    atomicBuffer.putBytes(16, data, 0, messageLength);
    long offered = aeronCluster.offer(atomicBuffer, 0, data.length + 16);
    while (offered == Publication.ADMIN_ACTION || offered == Publication.BACK_PRESSURED) {
      idleStrategy.idle();
      offered = aeronCluster.offer(atomicBuffer, 0, data.length);
    }
    log.trace("Sent message, {}", offered);
    if (offered >= 0) {
      keepAliveProcessor.getKeepAliveDeadlineMs().addAndGet(500);
    } else if (offered == Publication.CLOSED) {
      log.error("Publication is closed");
      aeronCluster.close();
      throw new ConnectionException("connection is closed", client.targetName(), message);

    } else {
      log.error("Failed to send message: {}", offered);
      return false;
    }
    return true;
  }

  @Override
  public boolean isConnected() {
    return this.aeronCluster != null && !this.aeronCluster.isClosed();
  }

  // The first two retries do not reject the message.
  @Override
  public boolean connectionAvailable() {
    return reconnectStrategy.getRetryCount() < 3;
  }

  @Override
  public boolean close() {
    if (aeronCluster != null) {
      aeronCluster.close();
      aeronCluster = null;
    }
    return true;
  }
}
